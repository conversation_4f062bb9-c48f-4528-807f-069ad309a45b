-- Drop existing tables if they exist (clean slate)
DROP TABLE IF EXISTS policies CASCADE;
DROP TABLE IF EXISTS educationdetails CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS beneficiaries CASCADE;
DROP TABLE IF EXISTS photos CASCADE;
DROP TABLE IF EXISTS agents CASCADE;
DROP TABLE IF EXISTS clients CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS admins CASCADE;

-- Create users table (matches FlutterFlow auth integration)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    email VARCHAR(255) UNIQUE NOT NULL
);

-- Create agents table with new structure (id as PK, agent_number nullable)
CREATE TABLE agents (
    id SERIAL PRIMARY KEY,
    agent_number VARCHAR(50) UNIQUE,  -- Admin assigns this, can be <PERSON><PERSON><PERSON> initially
    name <PERSON><PERSON><PERSON><PERSON>(255),
    ic_number VA<PERSON>HAR(20),
    gender VARCHAR(20),
    date_of_birth DATE,
    phone_number VA<PERSON>HA<PERSON>(20),
    address TEXT,
    beneficiary_phone VARCHAR(20),
    work_experience TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    photo VARCHAR(255)
);

-- Create clients table
CREATE TABLE clients (
    client_id VARCHAR(50) PRIMARY KEY,
    policy_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    name VARCHAR(255),
    ic_number VARCHAR(20),
    client_number VARCHAR(50),
    gender VARCHAR(20),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    marital_status VARCHAR(50),
    race VARCHAR(50),
    religion VARCHAR(50),
    nationality VARCHAR(50),
    occupation VARCHAR(100),
    exact_duties TEXT,
    nature_of_business TEXT,
    salary_yearly VARCHAR(20),
    company_name VARCHAR(255),
    company_address TEXT,
    weight VARCHAR(10),
    height VARCHAR(10),
    smoker VARCHAR(20),
    hospital_admission_history TEXT,
    status VARCHAR(20)
);

-- Create policies table
CREATE TABLE policies (
    policy_id VARCHAR(50) PRIMARY KEY,
    client_id VARCHAR(50) REFERENCES clients(client_id) ON DELETE CASCADE,
    agent_id INTEGER REFERENCES agents(id) ON DELETE SET NULL,  -- Now references agents.id
    plan_type VARCHAR(100),
    basic_plan_rider VARCHAR(100),
    sum_covered VARCHAR(50),
    coverage_term VARCHAR(10),
    contribution VARCHAR(50),
    start_date DATE,
    end_date DATE,
    status VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    policy_name VARCHAR(255)
);

-- Create educationdetails table
CREATE TABLE educationdetails (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER REFERENCES agents(id) ON DELETE CASCADE,  -- Now references agents.id
    level VARCHAR(100),
    year_completed VARCHAR(4),
    institution_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create documents table (FlutterFlow structure)
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    path VARCHAR(500)
);

-- Create beneficiaries table
CREATE TABLE beneficiaries (
    beneficiary_id VARCHAR(50) PRIMARY KEY,
    policy_id VARCHAR(50) REFERENCES policies(policy_id) ON DELETE CASCADE,
    name VARCHAR(255),
    ic_number VARCHAR(20),
    relationship VARCHAR(50),
    date_of_birth DATE,
    phone_number VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    percentage INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create photos table (FlutterFlow structure)
CREATE TABLE photos (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    path VARCHAR(500)
);

-- Create admins table (for PHP system)
CREATE TABLE admins (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create triggers for auto-creating agent records
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.users (id, email, created_at)
  VALUES (new.id, new.email, new.created_at);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.handle_new_agent()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.agents (user_id, created_at, updated_at)
  VALUES (new.id, new.created_at, new.created_at);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

DROP TRIGGER IF EXISTS on_user_created_create_agent ON public.users;
CREATE TRIGGER on_user_created_create_agent
  AFTER INSERT ON public.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_agent();

-- Insert default admin
INSERT INTO admins (username, password)
VALUES ('admin', crypt('admin123', gen_salt('bf')));